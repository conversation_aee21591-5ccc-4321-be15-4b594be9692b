"use client"

import { useState, useEffect } from "react"
import dynamic from "next/dynamic"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Banknote,
  Receipt,
  Pill,
  Heart,
  Thermometer,
  Stethoscope,
  User,
  Clock,
  Paperclip,
  Tag,
  FileText,
  ArrowRightLeft,
  X,
  UserCircle,
} from "lucide-react"

interface Product {
  id: string
  name: string
  price: number
  category: string
  stock: number
  prescription: boolean
  laboratory: string
  image: string
  sku: string
  barcode?: string
}

interface CartItem extends Product {
  quantity: number
}

// Utility function to format Chilean pesos
const formatCLP = (amount: number): string => {
  return new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'CLP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Utility function to get stock status
const getStockStatus = (stock: number) => {
  if (stock === 0) return 'out-of-stock';
  if (stock <= 5) return 'critical';
  if (stock <= 10) return 'low';
  return 'normal';
}

const sampleProducts: Product[] = [
  {
    id: "1",
    name: "Paracetamol 500 mg 16 Comprimidos",
    price: 959,
    category: "Analgésicos",
    stock: 50,
    prescription: false,
    laboratory: "PHARMARIS",
    image: "/medicamentos/paracetamol.webp",
    sku: "PHR-PAR-500-16"
  },
  {
    id: "2",
    name: "Ibuprofeno 200 mg 20 Comprimidos",
    price: 1443,
    category: "Antiinflamatorios",
    stock: 0,
    prescription: false,
    laboratory: "ASCEND",
    image: "/medicamentos/ibuprofeno.webp",
    sku: "ASC-IBU-200-20"
  },
  {
    id: "3",
    name: "Amoxicilina 500 mg 21 Cápsulas",
    price: 4268,
    category: "Antibióticos",
    stock: 25,
    prescription: true,
    laboratory: "MINTLAB",
    image: "/medicamentos/amoxicilina.webp",
    sku: "MIN-AMX-500-21"
  },
  {
    id: "4",
    name: "Omeprazol 20 mg 30 Cápsulas con Gránulos",
    price: 1751,
    category: "Digestivos",
    stock: 8,
    prescription: false,
    laboratory: "MDC",
    image: "/medicamentos/omeprazol.webp",
    sku: "MDC-OME-020-30"
  },
  {
    id: "5",
    name: "Loratadina 10 mg 30 Comprimidos",
    price: 1487,
    category: "Antihistamínicos",
    stock: 35,
    prescription: false,
    laboratory: "OPKO",
    image: "/medicamentos/loratadina.webp",
    sku: "OPK-LOR-010-30"
  },
  {
    id: "6",
    name: "Vitamina C 1000 mg Sabor Naranja 20 Tabletas Efervescentes",
    price: 3900,
    category: "Vitaminas",
    stock: 60,
    prescription: false,
    laboratory: "VITAMIN CHOICE",
    image: "/medicamentos/vitamina-c.webp",
    sku: "VTC-VIC-1000-20"
  },
  {
    id: "7",
    name: "Alcohol Desnaturalizado 70% 500 mL",
    price: 3190,
    category: "Antisépticos",
    stock: 80,
    prescription: false,
    laboratory: "DIFEM PHARMA",
    image: "/medicamentos/alcohol.webp",
    sku: "DIF-ALC-070-500"
  },
  {
    id: "8",
    name: "Termómetro Digital",
    price: 6990,
    category: "Dispositivos",
    stock: 3,
    prescription: false,
    laboratory: "VARIOS LABORATORIOS",
    image: "/medicamentos/termometro-digital.webp",
    sku: "VAR-TER-DIG-001"
  },
]

const categories = [
  { name: "Todos", icon: Pill },
  { name: "Analgésicos", icon: Heart },
  { name: "Antibióticos", icon: Stethoscope },
  { name: "Vitaminas", icon: Plus },
  { name: "Dispositivos", icon: Thermometer },
]

// Componente del reloj que se renderiza solo del lado del cliente
function ClockDisplay() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!mounted || !currentTime) {
    return <span className="font-mono w-16 text-center">--:--:--</span>
  }

  return (
    <span className="font-mono w-16 text-center">
      {currentTime.toLocaleTimeString('es-CL', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })}
    </span>
  )
}

export default function PharmacyPOS() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Todos")
  const [cart, setCart] = useState<CartItem[]>([])
  const [currentCustomer, setCurrentCustomer] = useState("")
  const [discountCode, setDiscountCode] = useState("")
  const [prescriptionFile, setPrescriptionFile] = useState<File | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)

  const getAvailableStock = (productId: string) => {
    const originalProduct = sampleProducts.find(p => p.id === productId)
    const cartItem = cart.find(item => item.id === productId)
    const quantityInCart = cartItem ? cartItem.quantity : 0

    if (!originalProduct) return 0
    return Math.max(0, originalProduct.stock - quantityInCart)
  }

  const filteredProducts = sampleProducts.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "Todos" || product.category === selectedCategory
    return matchesSearch && matchesCategory
  }).map(product => ({
    ...product,
    stock: getAvailableStock(product.id) // Usar stock dinámico
  }))

  const addToCart = (product: Product) => {
    const existing = cart.find((item) => item.id === product.id)
    const currentQuantityInCart = existing ? existing.quantity : 0

    // Obtener el stock original del producto
    const originalProduct = sampleProducts.find(p => p.id === product.id)
    if (!originalProduct) return

    // Verificar si hay stock suficiente (comparar con stock original)
    if (currentQuantityInCart >= originalProduct.stock) {
      return // No agregar si ya se alcanzó el stock máximo original
    }

    setCart((prev) => {
      if (existing) {
        return prev.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      }
      return [...prev, { ...product, quantity: 1 }]
    })
  }

  const updateQuantity = (id: string, change: number) => {
    const cartItem = cart.find(item => item.id === id)
    if (!cartItem) return

    const originalProduct = sampleProducts.find(p => p.id === id)
    if (!originalProduct) return

    const newQuantity = cartItem.quantity + change

    // Si es incremento, verificar que no exceda el stock original
    if (change > 0 && newQuantity > originalProduct.stock) {
      return // No permitir incrementar si excede el stock original
    }

    // Si la nueva cantidad es 0 o menor, eliminar del carrito
    if (newQuantity <= 0) {
      setCart((prev) => prev.filter((item) => item.id !== id))
      return
    }

    setCart((prev) =>
      prev.map((item) =>
        item.id === id
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }

  const removeFromCart = (id: string) => {
    setCart((prev) => prev.filter((item) => item.id !== id))
  }

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + item.price * item.quantity, 0)
  }

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }

  const processSale = () => {
    // Limpiar el carrito después de procesar la venta
    setCart([])
    setShowPaymentModal(false)
    // El stock ya está actualizado, no necesitamos hacer nada más
  }

  const handlePayment = (paymentMethod: string) => {
    // Aquí se procesaría el pago según el método seleccionado
    console.log(`Procesando pago con: ${paymentMethod}`)
    processSale()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-cyan-50">
      {/* Header Principal */}
      <header className="bg-primary text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo y Título */}
            <div className="flex items-center gap-3">
              <Image
                src="/logo/collapse-logo.png"
                alt="Andes Salud Logo"
                width={40}
                height={40}
                className="h-10 w-10 object-contain"
              />
              <div>
                <h1 className="text-xl font-bold">ANDES SALUD</h1>
                <p className="text-sm text-blue-100">Sistema POS</p>
              </div>
            </div>

            {/* Usuario y Reloj */}
            <div className="flex items-center gap-6">
              {/* Reloj */}
              <div className="flex items-center gap-2 text-blue-100">
                <Clock className="h-4 w-4" />
                <ClockDisplay />
              </div>

              {/* Avatar Usuario */}
              <div className="flex items-center gap-2">
                <UserCircle className="h-8 w-8 text-blue-100" />
                <div className="hidden sm:block">
                  <p className="text-sm font-medium">Farmacéutico</p>
                  <p className="text-xs text-blue-100">En línea</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6 p-4">
        {/* Panel Principal - Productos */}
        <div className="lg:col-span-2 space-y-4">

          {/* Búsqueda y Cliente */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Buscar medicamento o código..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-12 text-lg"
                  />
                </div>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Cliente"
                    value={currentCustomer}
                    onChange={(e) => setCurrentCustomer(e.target.value)}
                    className="pl-10 h-12 text-lg"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Receta y Código de Descuento */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => setPrescriptionFile(e.target.files?.[0] || null)}
                    className="hidden"
                    id="prescription-upload"
                  />
                  <Button
                    variant="outline"
                    className="w-full h-12 text-lg justify-start"
                    onClick={() => document.getElementById('prescription-upload')?.click()}
                  >
                    <Paperclip className="h-5 w-5 mr-2" />
                    {prescriptionFile ? prescriptionFile.name : 'Adjuntar Receta'}
                  </Button>
                </div>
                <div className="relative">
                  <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Código de descuento"
                    value={discountCode}
                    onChange={(e) => setDiscountCode(e.target.value)}
                    className="pl-10 h-12 text-lg"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Categorías */}
          <Card>
            <CardContent className="p-4">
              <div className="flex gap-2 overflow-x-auto">
                {categories.map((category) => {
                  const IconComponent = category.icon
                  return (
                    <Button
                      key={category.name}
                      variant={selectedCategory === category.name ? "default" : "outline"}
                      onClick={() => setSelectedCategory(category.name)}
                      className="flex items-center gap-2 whitespace-nowrap h-12 px-6 rounded-full"
                    >
                      <IconComponent className="h-5 w-5" />
                      {category.name}
                    </Button>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Grid de Productos */}
          <Card className="flex-1">
            <CardContent className="p-4 h-full">
              <ScrollArea className="h-full">
                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 min-h-full">
                  {filteredProducts.map((product) => {
                    const stockStatus = getStockStatus(product.stock);
                    return (
                    <Card
                      key={product.id}
                      className={`transition-all duration-200 border-2 h-[26rem] hover:shadow-lg ${
                        product.stock === 0
                          ? 'opacity-75 border-gray-300'
                          : 'hover:border-accent'
                      } ${
                        stockStatus === 'critical' ? 'border-red-500 shadow-red-100' :
                        stockStatus === 'low' ? 'border-orange-400 shadow-orange-100' : ''
                      }`}
                    >
                      <CardContent className="p-4 h-full">
                        <div className="flex flex-col h-full">
                          {/* Imagen del producto - ALTURA FIJA */}
                          <div className="relative w-full h-64 bg-white rounded-lg overflow-hidden mb-3">
                            <Image
                              src={product.image}
                              alt={product.name}
                              fill
                              className={`object-contain transition-all duration-200 ${
                                product.stock === 0 ? 'grayscale' : ''
                              }`}
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            />
                            {(() => {
                              const cartItem = cart.find(item => item.id === product.id)
                              const quantityInCart = cartItem ? cartItem.quantity : 0
                              return quantityInCart > 0 && (
                                <div className="absolute top-2 right-2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
                                  {quantityInCart}
                                </div>
                              )
                            })()}
                          </div>

                          {/* Información del producto - ALTURA FIJA */}
                          <div className="h-24 mb-3">
                            <div className="flex justify-between items-start h-full">
                              <div className="flex-1">
                                <p className="text-xs font-medium text-primary mb-1">{product.laboratory}</p>
                                <p className="text-xs text-gray-500 mb-2 font-mono">SKU: {product.sku}</p>
                                <h3 className="font-semibold text-sm leading-tight line-clamp-2">{product.name}</h3>
                              </div>
                              <div className="flex flex-col gap-1 ml-2">
                                {product.prescription && (
                                  <Badge variant="destructive" className="text-xs flex-shrink-0">
                                    Receta
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Categoría - ALTURA FIJA */}
                          <div className="h-6 mb-3">
                            <p className="text-xs text-gray-600">{product.category}</p>
                          </div>

                          {/* Precio y Stock - ALTURA FIJA */}
                          <div className="h-16 mb-3">
                            <div className="flex justify-between items-center h-full">
                              <span className="text-xl font-bold text-primary">{formatCLP(product.price)}</span>
                              <div className="flex flex-col items-end gap-1">
                                {getStockStatus(product.stock) === 'critical' && (
                                  <Badge variant="destructive" className="text-xs bg-red-600">
                                    ¡Stock Crítico!
                                  </Badge>
                                )}
                                {getStockStatus(product.stock) === 'low' && (
                                  <Badge variant="outline" className="text-xs border-orange-500 text-orange-600">
                                    Stock Bajo
                                  </Badge>
                                )}
                                <span className={`text-xs font-medium ${
                                  getStockStatus(product.stock) === 'out-of-stock' ? 'text-red-600' :
                                  getStockStatus(product.stock) === 'critical' ? 'text-red-500' :
                                  getStockStatus(product.stock) === 'low' ? 'text-orange-500' :
                                  'text-gray-500'
                                }`}>
                                  Stock: {product.stock}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Botón - POSICIÓN FIJA AL FINAL */}
                          <div className="mt-auto">
                            <Button
                              className={`w-full h-10 text-sm font-semibold rounded-full ${
                                product.stock === 0 ? 'cursor-not-allowed' : 'cursor-pointer'
                              }`}
                              disabled={product.stock === 0}
                              variant={product.stock === 0 ? "secondary" : "default"}
                              onClick={(e) => {
                                e.stopPropagation()
                                addToCart(product)
                              }}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              {product.stock === 0 ? 'Sin Stock' : 'Agregar'}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Panel Lateral - Carrito */}
        <div className="space-y-4 sticky top-4 h-fit">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <ShoppingCart className="h-6 w-6" />
                Carrito ({getTotalItems()})
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-64">
                <div className="p-4 space-y-3 min-h-full">
                  {cart.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Carrito vacío</p>
                  ) : (
                    cart.map((item) => (
                      <div key={item.id} className="flex items-center gap-3 p-3 bg-secondary/30 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm leading-tight">{item.name}</h4>
                          <p className="text-primary font-semibold">{formatCLP(item.price)}</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, -1)}
                            className="h-8 w-8 p-0 rounded-full"
                          >
                            <Minus className="h-4 w-4" />
                          </Button>

                          <span className="w-8 text-center font-semibold">{item.quantity}</span>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, 1)}
                            className="h-8 w-8 p-0 rounded-full"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>

                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeFromCart(item.id)}
                            className="h-8 w-8 p-0 ml-2 rounded-full"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Resumen y Pago */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-lg">
                  <span>Subtotal:</span>
                  <span>{formatCLP(getTotalAmount())}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>IVA (19%):</span>
                  <span>{formatCLP(getTotalAmount() * 0.19)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-xl font-bold text-primary">
                  <span>Total:</span>
                  <span>{formatCLP(getTotalAmount() * 1.19)}</span>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  className={`w-full h-14 text-lg font-semibold bg-primary hover:bg-primary/90 rounded-full ${
                    cart.length === 0 ? 'cursor-not-allowed' : 'cursor-pointer'
                  }`}
                  disabled={cart.length === 0}
                  onClick={() => setShowPaymentModal(true)}
                >
                  <Banknote className="h-6 w-6 mr-2" />
                  Pagar
                </Button>

                <Button variant="secondary" className="w-full h-12 rounded-full" disabled={cart.length === 0}>
                  <Receipt className="h-5 w-5 mr-2" />
                  Imprimir Ticket
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal de Métodos de Pago */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Seleccionar Método de Pago</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPaymentModal(false)}
                className="h-8 w-8 p-0 cursor-pointer"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-3">
              <Button
                className="w-full h-14 text-lg font-semibold justify-start rounded-full cursor-pointer"
                onClick={() => handlePayment('Efectivo')}
              >
                <Banknote className="h-6 w-6 mr-3" />
                Efectivo
              </Button>

              <Button
                variant="outline"
                className="w-full h-14 text-lg font-semibold justify-start rounded-full cursor-pointer"
                onClick={() => handlePayment('Cheque')}
              >
                <FileText className="h-6 w-6 mr-3" />
                Cheque
              </Button>

              <Button
                variant="outline"
                className="w-full h-14 text-lg font-semibold justify-start rounded-full cursor-pointer"
                onClick={() => handlePayment('Transferencia')}
              >
                <ArrowRightLeft className="h-6 w-6 mr-3" />
                Transferencia
              </Button>

              <Button
                variant="outline"
                className="w-full h-14 text-lg font-semibold justify-start rounded-full cursor-pointer"
                onClick={() => handlePayment('Tarjeta')}
              >
                <CreditCard className="h-6 w-6 mr-3" />
                Tarjeta
              </Button>
            </div>

            <div className="mt-6 pt-4 border-t">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total a pagar:</span>
                <span className="text-primary">{formatCLP(getTotalAmount() * 1.19)}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
