@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* ANDES SALUD Theme - White background with brand colors */
  --background: oklch(1 0 0); /* White */
  --foreground: oklch(0.25 0.15 250); /* Dark blue #003CA6 */
  --card: oklch(1 0 0); /* White */
  --card-foreground: oklch(0.25 0.15 250); /* Dark blue */
  --popover: oklch(1 0 0); /* White */
  --popover-foreground: oklch(0.25 0.15 250); /* Dark blue */
  --primary: oklch(0.25 0.15 250); /* #003CA6 - ANDES SALUD dark blue */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.85 0.08 180); /* #91E1D8 - Light mint */
  --secondary-foreground: oklch(0.25 0.15 250); /* Dark blue text */
  --muted: oklch(0.96 0 0); /* Very light gray */
  --muted-foreground: oklch(0.5 0.05 250); /* Muted blue */
  --accent: oklch(0.75 0.15 200); /* #00D9F9 - Cyan accent */
  --accent-foreground: oklch(0.25 0.15 250); /* Dark blue text */
  --destructive: oklch(0.577 0.245 27.325); /* Keep red for destructive */
  --border: oklch(0.9 0.02 200); /* Light cyan border */
  --input: oklch(0.98 0.01 200); /* Very light cyan input */
  --ring: oklch(0.75 0.15 200); /* Cyan ring */
  --chart-1: oklch(0.25 0.15 250); /* Primary blue */
  --chart-2: oklch(0.75 0.15 200); /* Cyan */
  --chart-3: oklch(0.85 0.08 180); /* Light mint */
  --chart-4: oklch(0.6 0.1 220); /* Medium blue */
  --chart-5: oklch(0.4 0.12 240); /* Darker blue */
  --sidebar: oklch(1 0 0); /* White sidebar */
  --sidebar-foreground: oklch(0.25 0.15 250); /* Dark blue text */
  --sidebar-primary: oklch(0.25 0.15 250); /* Primary blue */
  --sidebar-primary-foreground: oklch(1 0 0); /* White text */
  --sidebar-accent: oklch(0.85 0.08 180); /* Light mint accent */
  --sidebar-accent-foreground: oklch(0.25 0.15 250); /* Dark blue text */
  --sidebar-border: oklch(0.9 0.02 200); /* Light cyan border */
  --sidebar-ring: oklch(0.75 0.15 200); /* Cyan ring */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .force-static-header {
    position: static !important;
    display: block !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    z-index: auto !important;
    transform: none !important;
  }
}
