"use client"

import { useState } from "react"
import Header from "./components/Header"
import SearchAndFilters from "./components/SearchAndFilters"
import ProductGrid from "./components/ProductGrid"
import Cart from "./components/Cart"
import PaymentModal from "./components/PaymentModal"
import { useCart } from "./hooks/useCart"
import { sampleProducts, categories } from "./data"

export default function PharmacyPOS() {
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Todos")
  const [currentCustomer, setCurrentCustomer] = useState("")
  const [prescriptionFile, setPrescriptionFile] = useState<File | null>(null)
  const [discountCode, setDiscountCode] = useState("")
  
  // Payment modal state
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)

  // Cart logic using custom hook
  const {
    cart,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalItems,
    getSubtotal,
    getProductsWithUpdatedStock
  } = useCart(sampleProducts)

  // Get products with updated stock for display
  const productsWithUpdatedStock = getProductsWithUpdatedStock()

  const handlePaymentComplete = () => {
    clearCart()
    setCurrentCustomer("")
    setPrescriptionFile(null)
    setDiscountCode("")
    setIsPaymentModalOpen(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <Header />

      {/* Main Content with Fixed Height */}
      <div className="flex-1 overflow-hidden">
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6 p-4 h-full">
          {/* Main Panel - Products */}
          <div className="lg:col-span-2 flex flex-col space-y-4 min-h-0">
            {/* Search and Filters - Fixed Height */}
            <div className="flex-shrink-0">
              <SearchAndFilters
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
                categories={categories}
                currentCustomer={currentCustomer}
                onCustomerChange={setCurrentCustomer}
                prescriptionFile={prescriptionFile}
                onPrescriptionFileChange={setPrescriptionFile}
                discountCode={discountCode}
                onDiscountCodeChange={setDiscountCode}
              />
            </div>

            {/* Product Grid - Flexible Height with Scroll */}
            <div className="flex-1 min-h-0">
              <ProductGrid
                products={productsWithUpdatedStock}
                cart={cart}
                onAddToCart={addToCart}
                searchTerm={searchTerm}
                selectedCategory={selectedCategory}
              />
            </div>
          </div>

          {/* Side Panel - Cart - Sticky */}
          <div className="lg:sticky lg:top-24 lg:h-fit lg:max-h-[calc(100vh-8rem)]">
            <Cart
              cart={cart}
              onUpdateQuantity={updateQuantity}
              onRemoveItem={removeFromCart}
              onOpenPayment={() => setIsPaymentModalOpen(true)}
            />
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        cart={cart}
        onPaymentComplete={handlePaymentComplete}
      />
    </div>
  )
}
