"use client"

import { useState } from "react"
import Header from "./components/Header"
import SearchAndFilters from "./components/SearchAndFilters"
import ProductGrid from "./components/ProductGrid"
import Cart from "./components/Cart"
import PaymentModal from "./components/PaymentModal"
import { useCart } from "./hooks/useCart"
import { sampleProducts, categories } from "./data"

export default function PharmacyPOS() {
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Todos")
  const [currentCustomer, setCurrentCustomer] = useState("")
  const [prescriptionFile, setPrescriptionFile] = useState<File | null>(null)
  const [discountCode, setDiscountCode] = useState("")
  
  // Payment modal state
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)

  // Cart logic using custom hook
  const {
    cart,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalItems,
    getSubtotal,
    getProductsWithUpdatedStock
  } = useCart(sampleProducts)

  // Get products with updated stock for display
  const productsWithUpdatedStock = getProductsWithUpdatedStock()

  const handlePaymentComplete = () => {
    clearCart()
    setCurrentCustomer("")
    setPrescriptionFile(null)
    setDiscountCode("")
    setIsPaymentModalOpen(false)
  }

  return (
    <div className="bg-gray-50">
      <div className="max-w-7xl mx-auto p-4 space-y-6">
        {/* Contenido de prueba ANTES del header */}
        <div style={{ backgroundColor: 'red', color: 'white', padding: '10px' }}>
          CONTENIDO ANTES DEL HEADER - Si esto baja y el header no, hay un problema específico del header
        </div>

        {/* Header */}
        <Header />

        {/* Contenido de prueba DESPUÉS del header */}
        <div style={{ backgroundColor: 'green', color: 'white', padding: '20px', height: '200px' }}>
          CONTENIDO DESPUÉS DEL HEADER - Esto debería hacer scroll
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Panel - Products */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search and Filters */}
          <SearchAndFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            categories={categories}
            currentCustomer={currentCustomer}
            onCustomerChange={setCurrentCustomer}
            prescriptionFile={prescriptionFile}
            onPrescriptionFileChange={setPrescriptionFile}
            discountCode={discountCode}
            onDiscountCodeChange={setDiscountCode}
          />

          {/* Product Grid */}
          <ProductGrid
            products={productsWithUpdatedStock}
            cart={cart}
            onAddToCart={addToCart}
            searchTerm={searchTerm}
            selectedCategory={selectedCategory}
          />
        </div>

        {/* Side Panel - Cart */}
        <Cart
          cart={cart}
          onUpdateQuantity={updateQuantity}
          onRemoveItem={removeFromCart}
          onOpenPayment={() => setIsPaymentModalOpen(true)}
        />
        </div>

        {/* Contenido de prueba AL FINAL */}
        <div style={{ backgroundColor: 'purple', color: 'white', padding: '50px', height: '500px' }}>
          CONTENIDO AL FINAL - Si puedes ver esto haciendo scroll, el scroll funciona
        </div>
        <div style={{ backgroundColor: 'orange', color: 'black', padding: '50px', height: '500px' }}>
          MÁS CONTENIDO - Para forzar scroll largo
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        cart={cart}
        onPaymentComplete={handlePaymentComplete}
      />
    </div>
  )
}
