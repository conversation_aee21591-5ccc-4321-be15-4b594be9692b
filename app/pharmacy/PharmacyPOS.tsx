"use client"

import { useState } from "react"
import Header from "./components/Header"
import SearchAndFilters from "./components/SearchAndFilters"
import ProductGrid from "./components/ProductGrid"
import Cart from "./components/Cart"
import PaymentModal from "./components/PaymentModal"
import { useCart } from "./hooks/useCart"
import { sampleProducts, categories } from "./data"

export default function PharmacyPOS() {
  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Todos")
  const [currentCustomer, setCurrentCustomer] = useState("")
  const [prescriptionFile, setPrescriptionFile] = useState<File | null>(null)
  const [discountCode, setDiscountCode] = useState("")
  
  // Payment modal state
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)

  // Cart logic using custom hook
  const {
    cart,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalItems,
    getSubtotal,
    getProductsWithUpdatedStock
  } = useCart(sampleProducts)

  // Get products with updated stock for display
  const productsWithUpdatedStock = getProductsWithUpdatedStock()

  const handlePaymentComplete = () => {
    clearCart()
    setCurrentCustomer("")
    setPrescriptionFile(null)
    setDiscountCode("")
    setIsPaymentModalOpen(false)
  }

  return (
    <div className="bg-gray-50">
      <div className="max-w-7xl mx-auto p-4 space-y-6">
        {/* Header */}
        <Header />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Panel - Products */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search and Filters */}
          <SearchAndFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            categories={categories}
            currentCustomer={currentCustomer}
            onCustomerChange={setCurrentCustomer}
            prescriptionFile={prescriptionFile}
            onPrescriptionFileChange={setPrescriptionFile}
            discountCode={discountCode}
            onDiscountCodeChange={setDiscountCode}
          />

          {/* Product Grid */}
          <ProductGrid
            products={productsWithUpdatedStock}
            cart={cart}
            onAddToCart={addToCart}
            searchTerm={searchTerm}
            selectedCategory={selectedCategory}
          />
        </div>

        {/* Side Panel - Cart */}
        <Cart
          cart={cart}
          onUpdateQuantity={updateQuantity}
          onRemoveItem={removeFromCart}
          onOpenPayment={() => setIsPaymentModalOpen(true)}
        />
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        cart={cart}
        onPaymentComplete={handlePaymentComplete}
      />
    </div>
  )
}
