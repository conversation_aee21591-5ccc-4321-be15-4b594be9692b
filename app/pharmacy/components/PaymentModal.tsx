"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { 
  Banknote, 
  FileText, 
  ArrowRightLeft, 
  CreditCard,
  CheckCircle,
  X
} from "lucide-react"
import { PaymentMethod, CartItem } from '../types'
import { formatCLP, calculateIVA, calculateTotalWithIVA } from '../utils'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  cart: CartItem[]
  onPaymentComplete: () => void
}

export default function PaymentModal({ 
  isOpen, 
  onClose, 
  cart, 
  onPaymentComplete 
}: PaymentModalProps) {
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [amountReceived, setAmountReceived] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentCompleted, setPaymentCompleted] = useState(false)

  const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  const iva = calculateIVA(subtotal)
  const total = calculateTotalWithIVA(subtotal)

  const paymentMethods: { method: PaymentMethod; icon: any; label: string }[] = [
    { method: 'Efectivo', icon: Banknote, label: 'Efectivo' },
    { method: 'Cheque', icon: FileText, label: 'Cheque' },
    { method: 'Transferencia', icon: ArrowRightLeft, label: 'Transferencia' },
    { method: 'Tarjeta', icon: CreditCard, label: 'Tarjeta' },
  ]

  const handlePayment = async () => {
    if (!selectedPaymentMethod) return

    setIsProcessing(true)
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setIsProcessing(false)
    setPaymentCompleted(true)
    
    // Auto close after showing success
    setTimeout(() => {
      onPaymentComplete()
      handleClose()
    }, 2000)
  }

  const handleClose = () => {
    setSelectedPaymentMethod(null)
    setAmountReceived("")
    setIsProcessing(false)
    setPaymentCompleted(false)
    onClose()
  }

  const getChange = () => {
    if (selectedPaymentMethod !== 'Efectivo' || !amountReceived) return 0
    const received = parseFloat(amountReceived) || 0
    return Math.max(0, received - total)
  }

  const canProcessPayment = () => {
    if (!selectedPaymentMethod) return false
    if (selectedPaymentMethod === 'Efectivo') {
      const received = parseFloat(amountReceived) || 0
      return received >= total
    }
    return true
  }

  if (paymentCompleted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">¡Pago Completado!</h3>
            <p className="text-gray-600">La transacción se ha procesado exitosamente</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Métodos de Pago
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment Methods */}
          <div className="space-y-4">
            <h3 className="font-semibold">Seleccionar método de pago:</h3>
            <div className="grid grid-cols-2 gap-3">
              {paymentMethods.map(({ method, icon: Icon, label }) => (
                <Button
                  key={method}
                  variant={selectedPaymentMethod === method ? "default" : "outline"}
                  className="h-20 flex-col gap-2 rounded-xl"
                  onClick={() => setSelectedPaymentMethod(method)}
                >
                  <Icon className="h-6 w-6" />
                  <span className="text-sm">{label}</span>
                </Button>
              ))}
            </div>

            {/* Cash Payment Details */}
            {selectedPaymentMethod === 'Efectivo' && (
              <Card>
                <CardContent className="p-4 space-y-3">
                  <Label htmlFor="amount-received">Monto recibido:</Label>
                  <Input
                    id="amount-received"
                    type="number"
                    placeholder="Ingrese el monto"
                    value={amountReceived}
                    onChange={(e) => setAmountReceived(e.target.value)}
                    className="text-lg"
                  />
                  {getChange() > 0 && (
                    <div className="text-sm">
                      <span className="font-medium">Vuelto: </span>
                      <span className="text-green-600 font-bold">{formatCLP(getChange())}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary */}
          <div className="space-y-4">
            <h3 className="font-semibold">Resumen del pedido:</h3>
            <Card>
              <CardContent className="p-4 space-y-3">
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {cart.map((item) => (
                    <div key={item.id} className="flex justify-between text-sm">
                      <span>{item.name} x{item.quantity}</span>
                      <span>{formatCLP(item.price * item.quantity)}</span>
                    </div>
                  ))}
                </div>
                <Separator />
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>{formatCLP(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>IVA (19%):</span>
                    <span>{formatCLP(iva)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total:</span>
                    <span className="text-primary">{formatCLP(total)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Button
              className="w-full h-12 text-lg font-semibold rounded-full"
              disabled={!canProcessPayment() || isProcessing}
              onClick={handlePayment}
            >
              {isProcessing ? "Procesando..." : "Confirmar Pago"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
