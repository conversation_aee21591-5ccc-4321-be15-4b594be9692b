"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Clock } from "lucide-react"

// Componente del reloj que se renderiza solo del lado del cliente
function ClockDisplay() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!mounted || !currentTime) {
    return <span className="font-mono w-16 text-center">--:--:--</span>
  }

  return (
    <span className="font-mono w-16 text-center">
      {currentTime.toLocaleTimeString('es-CL', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })}
    </span>
  )
}

export default function Header() {
  return (
    <div style={{ backgroundColor: 'blue', color: 'white', padding: '20px', position: 'static', display: 'block' }}>
      <h1>HEADER DE PRUEBA - DEBERÍA BAJAR CON SCROLL</h1>
      <p>Si este header se queda pegado arriba, hay un problema muy raro</p>
    </div>
  )
}
