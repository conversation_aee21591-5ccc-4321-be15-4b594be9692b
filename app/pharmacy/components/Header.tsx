"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Clock, UserCircle } from "lucide-react"

// Componente del reloj que se renderiza solo del lado del cliente
function ClockDisplay() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!mounted || !currentTime) {
    return <span className="font-mono w-16 text-center">--:--:--</span>
  }

  return (
    <span className="font-mono w-16 text-center">
      {currentTime.toLocaleTimeString('es-CL', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })}
    </span>
  )
}

export default function Header() {
  return (
    <header className="bg-gradient-to-r from-primary to-primary/90 text-white shadow-lg">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between p-4">
          {/* Logo y Título */}
          <div className="flex items-center gap-4">
            <div className="relative w-12 h-12">
              <Image
                src="/logo/collapse-logo.png"
                alt="Andes Salud"
                fill
                className="object-contain"
                sizes="48px"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Andes Salud</h1>
              <p className="text-sm text-blue-100">Sistema POS Farmacia</p>
            </div>
          </div>

          {/* Usuario y Reloj */}
          <div className="flex items-center gap-6">
            {/* Reloj */}
            <div className="flex items-center gap-2 text-blue-100">
              <Clock className="h-4 w-4" />
              <ClockDisplay />
            </div>

            {/* Avatar Usuario */}
            <div className="flex items-center gap-2">
              <UserCircle className="h-8 w-8 text-blue-100" />
              <div className="hidden sm:block">
                <p className="text-sm font-medium">Farmacéutico</p>
                <p className="text-xs text-blue-100">En línea</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
