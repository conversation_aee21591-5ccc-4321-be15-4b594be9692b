"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Paperclip, Tag } from "lucide-react"
import { Product, CartItem } from '../types'
import { formatCLP, getStockStatus } from '../utils'

interface ProductCardProps {
  product: Product
  cart: CartItem[]
  onAddToCart: (product: Product) => void
}

export default function ProductCard({ product, cart, onAddToCart }: ProductCardProps) {
  const stockStatus = getStockStatus(product.stock)
  const cartItem = cart.find(item => item.id === product.id)
  const quantityInCart = cartItem ? cartItem.quantity : 0

  return (
    <Card
      className={`transition-all duration-200 border-2 h-[32rem] hover:shadow-lg ${
        product.stock === 0
          ? 'opacity-75 border-gray-300'
          : 'hover:border-accent'
      } ${
        stockStatus === 'critical' ? 'border-red-500 shadow-red-100' :
        stockStatus === 'low' ? 'border-orange-400 shadow-orange-100' : ''
      }`}
    >
      <CardContent className="p-4 h-full">
        <div className="grid grid-rows-[20px_200px_16px_16px_40px_auto_1fr] h-full gap-2">
          {/* Prescription badge - FILA 1 - ALTURA FIJA 24px */}
          <div className="flex justify-end items-start">
            {product.prescription && (
              <Badge variant="secondary" className="text-xs">
                <Paperclip className="h-3 w-3 mr-1" />
                Receta
              </Badge>
            )}
          </div>

          {/* Imagen del producto - FILA 2 - ALTURA FIJA 200px */}
          <div className="relative w-full bg-white rounded-lg overflow-hidden">
            <Image
              src={product.image}
              alt={product.name}
              fill
              className={`object-contain transition-all duration-200 ${
                product.stock === 0 ? 'grayscale' : ''
              }`}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            {quantityInCart > 0 && (
              <div className="absolute top-2 right-2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
                {quantityInCart}
              </div>
            )}
          </div>

          {/* Laboratorio - FILA 3 - ALTURA FIJA 16px */}
          <div className="flex items-center">
            <p className="text-xs font-medium text-primary truncate">{product.laboratory}</p>
          </div>

          {/* SKU - FILA 4 - ALTURA FIJA 16px */}
          <div className="flex items-center">
            <p className="text-xs text-gray-500 font-mono">SKU: {product.sku}</p>
          </div>

          {/* Título - FILA 5 - ALTURA FIJA 40px */}
          <div className="flex items-start">
            <h3 className="font-semibold text-sm leading-tight line-clamp-2">{product.name}</h3>
          </div>

          {/* Grid 2x2: Categoría/Precio y Stock - FILA 6 */}
          <div className="grid grid-cols-2 gap-2">
            {/* Columna 1 - Categoría y Precio */}
            <div className="space-y-1">
              <p className="text-xs text-gray-600 truncate">{product.category}</p>
              <p className="text-lg font-bold text-primary">{formatCLP(product.price)}</p>
            </div>

            {/* Columna 2 - Stock */}
            <div className="space-y-1">
              {/* Alerta de stock */}
              <div className="min-h-[20px]">
                {stockStatus === 'critical' && (
                  <Badge variant="destructive" className="text-xs">
                    ¡Stock Crítico!
                  </Badge>
                )}
                {stockStatus === 'low' && (
                  <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                    Stock Bajo
                  </Badge>
                )}
              </div>

              {/* Información de stock */}
              <div className="flex items-center gap-1">
                <Tag className="h-4 w-4 text-gray-500" />
                <span className={`font-medium text-sm ${
                  product.stock === 0 ? 'text-red-600' :
                  product.stock <= 5 ? 'text-red-500' :
                  product.stock <= 10 ? 'text-orange-500' : 'text-green-600'
                }`}>
                  Stock: {product.stock}
                </span>
              </div>
            </div>
          </div>

          {/* Stock, SKU y Botón - FILA 7 - FLEXIBLE */}
          <div className="flex flex-col justify-end space-y-2">
            {/* Stock en formato 2x2 - Alerta y Stock en la misma línea */}
            <div className="flex items-center justify-between gap-2">
              {/* Lado izquierdo - Alerta de stock */}
              <div className="flex-1">
                {stockStatus === 'critical' && (
                  <Badge variant="destructive" className="text-xs">
                    ¡Stock Crítico!
                  </Badge>
                )}
                {stockStatus === 'low' && (
                  <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                    Stock Bajo
                  </Badge>
                )}
              </div>

              {/* Lado derecho - Información de stock */}
              <div className="flex items-center gap-1">
                <Tag className="h-4 w-4 text-gray-500" />
                <span className={`font-medium text-sm ${
                  product.stock === 0 ? 'text-red-600' :
                  product.stock <= 5 ? 'text-red-500' :
                  product.stock <= 10 ? 'text-orange-500' : 'text-green-600'
                }`}>
                  Stock: {product.stock}
                </span>
              </div>
            </div>

            {/* SKU */}
            <p className="text-xs text-gray-500 font-mono">SKU: {product.sku}</p>

            {/* Botón */}
            <Button
              className={`w-full h-10 text-sm font-semibold rounded-full ${
                product.stock === 0 ? 'cursor-not-allowed' : 'cursor-pointer'
              }`}
              disabled={product.stock === 0}
              variant={product.stock === 0 ? "secondary" : "default"}
              onClick={(e) => {
                e.stopPropagation()
                onAddToCart(product)
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              {product.stock === 0 ? 'Sin Stock' : 'Agregar'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
