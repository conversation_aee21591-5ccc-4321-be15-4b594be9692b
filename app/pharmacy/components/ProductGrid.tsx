"use client"

import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Product, CartItem } from '../types'
import ProductCard from './ProductCard'

interface ProductGridProps {
  products: Product[]
  cart: CartItem[]
  onAddToCart: (product: Product) => void
  searchTerm?: string
  selectedCategory?: string
}

export default function ProductGrid({ 
  products, 
  cart, 
  onAddToCart,
  searchTerm = "",
  selectedCategory = "Todos"
}: ProductGridProps) {
  
  // Filter products based on search term and category
  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.laboratory.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "Todos" || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <Card className="flex-1">
      <CardContent className="p-4 h-full">
        <ScrollArea className="h-full">
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 min-h-full">
            {filteredProducts.length === 0 ? (
              <div className="col-span-full flex items-center justify-center py-12">
                <div className="text-center">
                  <p className="text-gray-500 text-lg mb-2">No se encontraron productos</p>
                  <p className="text-gray-400 text-sm">
                    {searchTerm ? `No hay productos que coincidan con "${searchTerm}"` : 
                     selectedCategory !== "Todos" ? `No hay productos en la categoría "${selectedCategory}"` :
                     "No hay productos disponibles"}
                  </p>
                </div>
              </div>
            ) : (
              filteredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  cart={cart}
                  onAddToCart={onAddToCart}
                />
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
